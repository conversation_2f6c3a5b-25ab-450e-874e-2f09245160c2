{% extends "base-modern.twig" %}

{% block title %}{{ user.id ? __('users.edit_user') : __('users.add_user') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ user.id ? __('users.edit_user') : __('users.add_user') }}</h1>
        <a href="{{ base_url }}/users" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>{{ __('common.back_to_list') }}
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <form method="POST" action="{{ user.id ? base_url ~ '/users/' ~ user.id : base_url ~ '/users' }}" 
                  enctype="multipart/form-data" class="needs-validation" novalidate id="userEditForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                <!-- Hidden submit button to catch Enter key -->
                <button type="submit" style="position: absolute; left: -9999px; width: 1px; height: 1px;" tabindex="-1" aria-hidden="true"></button>
                
                <!-- Basic Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-person-fill me-2"></i>{{ __('common.basic_information') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="first_name" class="form-label">{{ __('users.first_name') }} *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="{{ user.first_name }}" required>
                                <div class="invalid-feedback">
                                    {{ __('validation.required') }}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="last_name" class="form-label">{{ __('users.last_name') }} *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="{{ user.last_name }}" required>
                                <div class="invalid-feedback">
                                    {{ __('validation.required') }}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="username" class="form-label">{{ __('users.username') }} *</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="{{ user.username }}" required>
                                <div class="invalid-feedback">
                                    {{ __('validation.required') }}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="email" class="form-label">{{ __('common.email') }} *</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ user.email }}" required>
                                <div class="invalid-feedback">
                                    {{ __('validation.email') }}
                                </div>
                            </div>
                            
                            {% if not user.id %}
                            <div class="col-md-6">
                                <label for="password" class="form-label">{{ __('users.password') }} *</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback">
                                    {{ __('validation.required') }}
                                </div>
                                <small class="text-muted">{{ __('users.password_requirements') }}</small>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="password_confirmation" class="form-label">{{ __('users.confirm_password') }} *</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password_confirmation" 
                                           name="password_confirmation" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password_confirmation')">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                                <div class="invalid-feedback">
                                    {{ __('validation.password_match') }}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Profile Picture -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-camera-fill me-2"></i>{{ __('users.profile_picture') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                {% if user.avatar %}
                                    <img src="{{ base_url }}/uploads/avatars/{{ user.avatar }}" 
                                         class="rounded-circle" style="width: 100px; height: 100px;" 
                                         alt="{{ user.first_name ~ ' ' ~ user.last_name }}" id="avatarPreview">
                                {% else %}
                                    <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center" 
                                         style="width: 100px; height: 100px;" id="avatarPreview">
                                        <i class="bi bi-person-fill fs-1"></i>
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col">
                                <div class="mb-2">
                                    <input type="file" class="form-control" id="avatar" name="avatar" accept="image/*">
                                    <small class="text-muted">{{ __('users.avatar_hint') }}</small>
                                </div>
                                {% if user.avatar %}
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="remove_avatar" name="remove_avatar">
                                    <label class="form-check-label" for="remove_avatar">
                                        {{ __('users.remove_avatar') }}
                                    </label>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Address Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-geo-alt-fill me-2"></i>{{ __('users.address_information') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label for="address" class="form-label">{{ __('common.address') }}</label>
                                <input type="text" class="form-control" id="address" name="address" 
                                       value="{{ user.address|default('15, am Pëtz') }}">
                            </div>
                            
                            <div class="col-md-4">
                                <label for="postal_code" class="form-label">{{ __('common.postal_code') }}</label>
                                <input type="text" class="form-control" id="postal_code" name="postal_code" 
                                       value="{{ user.postal_code|default('L-9579') }}">
                            </div>
                            
                            <div class="col-md-4">
                                <label for="city" class="form-label">{{ __('common.city') }}</label>
                                <input type="text" class="form-control" id="city" name="city" 
                                       value="{{ user.city|default('Weidingen') }}">
                            </div>
                            
                            <div class="col-md-4">
                                <label for="country" class="form-label">{{ __('common.country') }}</label>
                                <select class="form-select" id="country" name="country">
                                    <option value="LU" {{ (user.country|default('LU')) == 'LU' ? 'selected' : '' }}>Luxembourg</option>
                                    <option value="FR" {{ user.country == 'FR' ? 'selected' : '' }}>France</option>
                                    <option value="BE" {{ user.country == 'BE' ? 'selected' : '' }}>Belgique</option>
                                    <option value="DE" {{ user.country == 'DE' ? 'selected' : '' }}>Allemagne</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tax Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-receipt-cutoff me-2"></i>{{ __('users.tax_information') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label for="vat_intercommunautaire" class="form-label">{{ __('users.vat_intercommunautaire') }}</label>
                                <input type="text" class="form-control" id="vat_intercommunautaire" name="vat_intercommunautaire" 
                                       value="{{ user.vat_intercommunautaire }}"
                                       placeholder="LU12345678">
                                <small class="text-muted">{{ __('users.vat_intercommunautaire_hint') }}</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Access Control -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-shield-lock-fill me-2"></i>{{ __('users.access_control') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="is_active" class="form-label">{{ __('common.status') }}</label>
                                <select class="form-select" id="is_active" name="is_active">
                                    <option value="1" {{ user.is_active ? 'selected' : '' }}>{{ __('common.active') }}</option>
                                    <option value="0" {{ not user.is_active ? 'selected' : '' }}>{{ __('common.inactive') }}</option>
                                </select>
                            </div>
                            
                            <div class="col-md-12">
                                <label class="form-label">{{ __('users.user_groups') }}</label>
                                <div class="row">
                                    {% for group in groups %}
                                    <div class="col-md-4 mb-2">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" 
                                                   id="group_{{ group.id }}" name="groups[]" 
                                                   value="{{ group.id }}"
                                                   {{ group.id in user_groups ? 'checked' : '' }}>
                                            <label class="form-check-label" for="group_{{ group.id }}">
                                                <span class="badge" style="background-color: {{ group.color }};">
                                                    <i class="{{ group.icon }} me-1"></i>{{ group.name }}
                                                </span>
                                            </label>
                                        </div>
                                    </div>
                                    {% endfor %}
                                </div>
                                <small class="text-muted">{{ __('users.help_groups') }}</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Retrocession Settings -->
                {% if user.id %}
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-calculator me-2"></i>{{ __('users.retrocession_settings') }}
                            <button type="button" class="btn btn-sm btn-light float-end" onclick="toggleRetrocessionSettings()">
                                <i class="bi bi-plus-circle me-1"></i>{{ __('users.add_retrocession_settings') }}
                            </button>
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Current Settings Display -->
                        <div id="currentRetrocessionSettings">
                            {% if retrocession_settings %}
                                <div class="alert alert-success">
                                    <h6 class="alert-heading">{{ __('users.current_active_settings') }}</h6>
                                    <p class="mb-1">
                                        <strong>{{ __('config.cns') }}:</strong> 
                                        {% if retrocession_settings.cns_type == 'percentage' %}
                                            {{ retrocession_settings.cns_value }} %
                                        {% else %}
                                            {{ retrocession_settings.cns_value }} {{ currency }}
                                        {% endif %}
                                    </p>
                                    <p class="mb-1">
                                        <strong>{{ __('config.patient') }}:</strong> 
                                        {% if retrocession_settings.patient_type == 'percentage' %}
                                            {{ retrocession_settings.patient_value }} %
                                        {% else %}
                                            {{ retrocession_settings.patient_value }} {{ currency }}
                                        {% endif %}
                                    </p>
                                    <p class="mb-1">
                                        <strong>{{ __('config.secretary_fees') }}:</strong> 
                                        {% if retrocession_settings.secretary_type == 'percentage' %}
                                            {{ retrocession_settings.secretary_value }} %
                                        {% else %}
                                            {{ retrocession_settings.secretary_value }} {{ currency }}
                                        {% endif %}
                                    </p>
                                    {% if retrocession_settings.ceiling_enabled %}
                                    <p class="mb-0">
                                        <strong>{{ __('config.ceiling') }}:</strong> {{ retrocession_settings.ceiling_amount }} {{ currency }}
                                    </p>
                                    {% endif %}
                                    <hr>
                                    <small class="text-muted">
                                        {{ __('users.valid_from') }}: {{ retrocession_settings.valid_from|date('d/m/Y') }}
                                        {% if retrocession_settings.valid_to %}
                                            - {{ __('users.valid_to') }}: {{ retrocession_settings.valid_to|date('d/m/Y') }}
                                        {% endif %}
                                    </small>
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    {{ __('users.no_specific_retrocession_settings') }}
                                </div>
                            {% endif %}
                            
                            <!-- View History Button -->
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="viewRetrocessionHistory({{ user.id }})">
                                <i class="bi bi-clock-history me-1"></i>{{ __('users.view_history') }}
                            </button>
                        </div>
                        
                        <!-- New Settings Form (Hidden by default) -->
                        <div id="newRetrocessionSettings" style="display: none;">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">{{ __('users.valid_from') }} *</label>
                                    <input type="date" class="form-control" name="retrocession[valid_from]" 
                                           min="{{ 'now'|date('Y-m-d') }}" value="{{ 'now'|date('Y-m-d') }}">
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">{{ __('users.valid_to') }}</label>
                                    <input type="date" class="form-control" name="retrocession[valid_to]">
                                    <small class="text-muted">{{ __('users.leave_empty_for_indefinite') }}</small>
                                </div>
                                
                                <!-- CNS Settings -->
                                <div class="col-12">
                                    <h6 class="text-primary">{{ __('config.cns_settings') }}</h6>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="retrocession[cns_type]" 
                                               id="cns_percentage" value="percentage" checked onchange="toggleValueType('cns')">
                                        <label class="form-check-label" for="cns_percentage">{{ __('common.percentage') }}</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="retrocession[cns_type]" 
                                               id="cns_fixed" value="fixed_amount" onchange="toggleValueType('cns')">
                                        <label class="form-check-label" for="cns_fixed">{{ __('common.fixed_amount') }}</label>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="retrocession[cns_value]" 
                                               id="cns_value" step="0.01" min="0" value="20.00">
                                        <span class="input-group-text" id="cns_suffix">%</span>
                                    </div>
                                </div>
                                
                                <!-- Patient Settings -->
                                <div class="col-12">
                                    <h6 class="text-primary">{{ __('config.patient_settings') }}</h6>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="retrocession[patient_type]" 
                                               id="patient_percentage" value="percentage" checked onchange="toggleValueType('patient')">
                                        <label class="form-check-label" for="patient_percentage">{{ __('common.percentage') }}</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="retrocession[patient_type]" 
                                               id="patient_fixed" value="fixed_amount" onchange="toggleValueType('patient')">
                                        <label class="form-check-label" for="patient_fixed">{{ __('common.fixed_amount') }}</label>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="retrocession[patient_value]" 
                                               id="patient_value" step="0.01" min="0" value="20.00">
                                        <span class="input-group-text" id="patient_suffix">%</span>
                                    </div>
                                </div>
                                
                                <!-- Secretary Fees Settings -->
                                <div class="col-12">
                                    <h6 class="text-primary">{{ __('config.secretary_fees_settings') }}</h6>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="retrocession[secretary_type]" 
                                               id="secretary_percentage" value="percentage" checked onchange="toggleValueType('secretary')">
                                        <label class="form-check-label" for="secretary_percentage">{{ __('common.percentage') }}</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="radio" name="retrocession[secretary_type]" 
                                               id="secretary_fixed" value="fixed_amount" onchange="toggleValueType('secretary')">
                                        <label class="form-check-label" for="secretary_fixed">{{ __('common.fixed_amount') }}</label>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="input-group">
                                        <input type="number" class="form-control" name="retrocession[secretary_value]" 
                                               id="secretary_value" step="0.01" min="0" value="10.00">
                                        <span class="input-group-text" id="secretary_suffix">%</span>
                                    </div>
                                </div>
                                
                                <!-- Ceiling Settings -->
                                <div class="col-12">
                                    <hr>
                                    <div class="form-check form-switch mb-3">
                                        <input type="checkbox" class="form-check-input" id="ceiling_enabled" 
                                               name="retrocession[ceiling_enabled]" value="1" onchange="toggleCeiling()">
                                        <label class="form-check-label" for="ceiling_enabled">
                                            {{ __('config.enable_invoice_ceiling') }}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6" id="ceiling_amount_group" style="display: none;">
                                    <label class="form-label">{{ __('config.ceiling_amount') }} ({{ currency }})</label>
                                    <input type="number" class="form-control" name="retrocession[ceiling_amount]" 
                                           step="0.01" min="0" value="5000.00">
                                </div>
                                
                                <!-- Notes -->
                                <div class="col-12">
                                    <label class="form-label">{{ __('common.notes') }}</label>
                                    <textarea class="form-control" name="retrocession[notes]" rows="2"></textarea>
                                </div>
                                
                                <!-- Buttons -->
                                <div class="col-12">
                                    <button type="button" class="btn btn-secondary" onclick="cancelRetrocessionSettings()">
                                        {{ __('common.cancel') }}
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="validateRetrocessionSettings()">
                                        {{ __('common.save') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <!-- Financial Obligations -->
                {% if user.id and (financial_obligations or can_edit_financial) %}
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-warning text-dark">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="bi bi-cash-coin me-2"></i>{{ __('users.financial_obligations') }}
                            </h5>
                            <a href="{{ base_url }}/users/{{ user.id }}/financial-obligations" class="btn btn-sm btn-light">
                                <i class="bi bi-eye me-1"></i>{{ __('common.view_all') }}
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        {% if financial_obligations %}
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label class="form-label text-muted">{{ __('users.rent_amount') }}</label>
                                    <div class="form-control-plaintext">
                                        <strong>{{ financial_obligations.rent_amount|number_format(2, ',', '.') }}€</strong>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label text-muted">{{ __('users.charges_amount') }}</label>
                                    <div class="form-control-plaintext">
                                        <strong>{{ financial_obligations.charges_amount|number_format(2, ',', '.') }}€</strong>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label text-muted">{{ __('users.total_tvac') }}</label>
                                    <div class="form-control-plaintext">
                                        <strong class="text-primary">{{ financial_obligations.total_tvac|number_format(2, ',', '.') }}€</strong>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label text-muted">{{ __('users.effective_date') }}</label>
                                    <div class="form-control-plaintext">
                                        <strong>{{ financial_obligations.effective_date|date('d/m/Y') }}</strong>
                                    </div>
                                </div>
                            </div>
                            
                            {% if not can_edit_financial %}
                            <div class="alert alert-info mt-3 mb-0">
                                <i class="bi bi-info-circle me-2"></i>
                                {{ __('users.contact_admin_for_changes') }}
                            </div>
                            {% endif %}
                        {% else %}
                            <div class="alert alert-warning mb-0">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                {{ __('users.no_financial_obligations') }}
                                {% if can_edit_financial %}
                                    <a href="{{ base_url }}/users/{{ user.id }}/financial-obligations" class="alert-link">
                                        {{ __('users.add_financial_obligations') }}
                                    </a>
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
                
                <!-- Additional Settings -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-gear-fill me-2"></i>{{ __('users.additional_settings') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="language" class="form-label">{{ __('users.preferred_language') }}</label>
                                <select class="form-select" id="language" name="language">
                                    <option value="fr" {{ user.language == 'fr' ? 'selected' : '' }}>Français</option>
                                    <option value="en" {{ user.language == 'en' ? 'selected' : '' }}>English</option>
                                    <option value="de" {{ user.language == 'de' ? 'selected' : '' }}>Deutsch</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="timezone" class="form-label">{{ __('users.timezone') }}</label>
                                <select class="form-select" id="timezone" name="timezone">
                                    <option value="Europe/Luxembourg" {{ user.timezone == 'Europe/Luxembourg' ? 'selected' : '' }}>
                                        Europe/Luxembourg
                                    </option>
                                    <option value="Europe/Paris" {{ user.timezone == 'Europe/Paris' ? 'selected' : '' }}>
                                        Europe/Paris
                                    </option>
                                    <option value="Europe/Brussels" {{ user.timezone == 'Europe/Brussels' ? 'selected' : '' }}>
                                        Europe/Brussels
                                    </option>
                                    <option value="Europe/Berlin" {{ user.timezone == 'Europe/Berlin' ? 'selected' : '' }}>
                                        Europe/Berlin
                                    </option>
                                </select>
                            </div>
                            
                            <div class="col-md-12">
                                <label for="notes" class="form-label">{{ __('common.notes') }}</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3">{{ user.notes }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="d-flex justify-content-between">
                    {% if user.id %}
                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                        <i class="bi bi-trash me-2"></i>{{ __('common.delete') }}
                    </button>
                    {% else %}
                    <div></div>
                    {% endif %}
                    <div class="d-flex gap-2">
                        <a href="{{ base_url }}/users" class="btn btn-secondary">
                            <i class="bi bi-x-circle me-2"></i>{{ __('common.cancel') }}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>{{ user.id ? __('common.save_changes') : __('common.save') }}
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Side Information -->
        <div class="col-lg-4">
            {% if user.id %}
            <!-- User Info -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="bi bi-info-circle-fill me-2"></i>{{ __('users.user_info') }}</h5>
                </div>
                <div class="card-body">
                    <dl class="row mb-0">
                        <dt class="col-sm-5">{{ __('users.user_id') }}:</dt>
                        <dd class="col-sm-7">{{ user.id }}</dd>
                        
                        <dt class="col-sm-5">{{ __('common.created_at') }}:</dt>
                        <dd class="col-sm-7">{{ user.created_at|date('d/m/Y H:i') }}</dd>
                        
                        <dt class="col-sm-5">{{ __('common.updated_at') }}:</dt>
                        <dd class="col-sm-7">{{ user.updated_at|date('d/m/Y H:i') }}</dd>
                        
                        <dt class="col-sm-5">{{ __('users.last_login') }}:</dt>
                        <dd class="col-sm-7">
                            {% if user.last_login_at %}
                                {{ user.last_login_at|date('d/m/Y H:i') }}
                            {% else %}
                                <span class="text-muted">{{ __('users.never') }}</span>
                            {% endif %}
                        </dd>
                    </dl>
                </div>
            </div>
            {% endif %}
            
            
            <!-- Help -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-question-circle me-2"></i>{{ __('common.help') }}</h5>
                </div>
                <div class="card-body">
                    <p class="mb-2"><i class="bi bi-info-circle text-info me-2"></i>{{ __('users.help_username') }}</p>
                    <p class="mb-0"><i class="bi bi-people text-success me-2"></i>{{ __('users.help_groups') }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Form (hidden) -->
{% if user.id %}
<form id="deleteForm" method="POST" action="{{ base_url }}/users/{{ user.id }}" style="display: none;">
    <input type="hidden" name="_method" value="DELETE">
    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
</form>
{% endif %}

<script>
// Avatar preview
document.getElementById('avatar').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('avatarPreview');
            if (preview.tagName === 'IMG') {
                preview.src = e.target.result;
            } else {
                const img = document.createElement('img');
                img.src = e.target.result;
                img.className = 'rounded-circle';
                img.style.width = '100px';
                img.style.height = '100px';
                img.id = 'avatarPreview';
                preview.parentNode.replaceChild(img, preview);
            }
        };
        reader.readAsDataURL(file);
    }
});

// Password toggle
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        button.classList.remove('bi-eye');
        button.classList.add('bi-eye-slash');
    } else {
        field.type = 'password';
        button.classList.remove('bi-eye-slash');
        button.classList.add('bi-eye');
    }
}

// Auto-generate email based on first name
function generateEmail() {
    const firstNameField = document.getElementById('first_name');
    const emailField = document.getElementById('email');
    
    // Only generate for new users and if email is empty or not @fit-360.lu
    if (!{{ user.id|default('null') }} && firstNameField.value) {
        const currentEmail = emailField.value;
        
        // Check if email is empty or not using our domain
        if (!currentEmail || !currentEmail.endsWith('@fit-360.lu')) {
            // Clean the first name
            let cleanName = firstNameField.value.toLowerCase();
            
            // Remove accents
            cleanName = cleanName.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
            
            // Remove special characters and spaces
            cleanName = cleanName.replace(/[^a-z0-9]/g, '');
            
            // Generate email
            if (cleanName) {
                emailField.value = cleanName + '@fit-360.lu';
            }
        }
    }
}

// Add event listener for first name field
document.getElementById('first_name').addEventListener('blur', generateEmail);
document.getElementById('first_name').addEventListener('change', generateEmail);

// Auto-generate username based on first name and last name
function generateUsername() {
    const firstNameField = document.getElementById('first_name');
    const lastNameField = document.getElementById('last_name');
    const usernameField = document.getElementById('username');
    
    // Only generate for new users and if username is empty
    if (!{{ user.id|default('null') }} && firstNameField.value && !usernameField.value) {
        // Clean the first name
        let cleanFirstName = firstNameField.value.toLowerCase();
        
        // Remove accents
        cleanFirstName = cleanFirstName.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
        
        // Remove special characters and spaces
        cleanFirstName = cleanFirstName.replace(/[^a-z0-9]/g, '');
        
        // Generate username
        if (cleanFirstName) {
            usernameField.value = cleanFirstName;
            
            // If we have last name, prepare it for potential use
            if (lastNameField.value) {
                let cleanLastName = lastNameField.value.toLowerCase();
                cleanLastName = cleanLastName.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
                cleanLastName = cleanLastName.replace(/[^a-z0-9]/g, '');
                
                // Store as data attribute for server-side fallback
                usernameField.setAttribute('data-lastname-letter', cleanLastName.charAt(0));
            }
        }
    }
}

// Add event listeners for username generation
document.getElementById('first_name').addEventListener('blur', generateUsername);
document.getElementById('last_name').addEventListener('blur', generateUsername);

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        // Disable retrocession fields on page load
        const newRetrocessionSettings = document.getElementById('newRetrocessionSettings');
        if (newRetrocessionSettings) {
            newRetrocessionSettings.querySelectorAll('input, select, textarea').forEach(field => {
                field.disabled = true;
            });
        }
        
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                // Check password match
                const password = document.getElementById('password');
                const confirmation = document.getElementById('password_confirmation');
                if (password && confirmation && password.value !== confirmation.value) {
                    confirmation.setCustomValidity('Passwords do not match');
                } else if (confirmation) {
                    confirmation.setCustomValidity('');
                }
                
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                } else {
                    // Form is valid, let it submit normally
                    console.log('Form submitting to:', form.action);
                    console.log('Method:', form.method);
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

function confirmDelete() {
    if (confirm('{{ __("users.delete_confirm") }}')) {
        document.getElementById('deleteForm').submit();
    }
}

// Retrocession Settings Functions
function toggleRetrocessionSettings() {
    const currentSettings = document.getElementById('currentRetrocessionSettings');
    const newSettings = document.getElementById('newRetrocessionSettings');
    const button = document.querySelector('[onclick="toggleRetrocessionSettings()"]');
    
    if (newSettings.style.display === 'none') {
        currentSettings.style.display = 'none';
        newSettings.style.display = 'block';
        button.innerHTML = '<i class="bi bi-x-circle me-1"></i>{{ __("common.cancel") }}';
        // Enable all retrocession input fields
        newSettings.querySelectorAll('input, select, textarea').forEach(field => {
            field.disabled = false;
        });
    } else {
        currentSettings.style.display = 'block';
        newSettings.style.display = 'none';
        button.innerHTML = '<i class="bi bi-plus-circle me-1"></i>{{ __("users.add_retrocession_settings") }}';
        // Disable all retrocession input fields to prevent submission
        newSettings.querySelectorAll('input, select, textarea').forEach(field => {
            field.disabled = true;
        });
    }
}

function cancelRetrocessionSettings() {
    toggleRetrocessionSettings();
    // Reset form values
    document.querySelectorAll('#newRetrocessionSettings input[type="number"]').forEach(input => {
        input.value = input.defaultValue;
    });
    document.querySelectorAll('#newRetrocessionSettings input[type="radio"]:first-of-type').forEach(radio => {
        radio.checked = true;
    });
}

function toggleValueType(type) {
    const suffix = document.getElementById(type + '_suffix');
    const valueInput = document.getElementById(type + '_value');
    const isPercentage = document.getElementById(type + '_percentage').checked;
    
    if (isPercentage) {
        suffix.textContent = '%';
        valueInput.max = '100';
        if (parseFloat(valueInput.value) > 100) {
            valueInput.value = '20.00';
        }
    } else {
        suffix.textContent = '{{ currency }}';
        valueInput.removeAttribute('max');
    }
}

function toggleCeiling() {
    const enabled = document.getElementById('ceiling_enabled').checked;
    const amountGroup = document.getElementById('ceiling_amount_group');
    
    if (enabled) {
        amountGroup.style.display = 'block';
    } else {
        amountGroup.style.display = 'none';
    }
}

function validateRetrocessionSettings() {
    const form = document.getElementById('newRetrocessionSettings');
    const inputs = form.querySelectorAll('input[required]');
    let isValid = true;
    
    // Validate date range
    const validFrom = form.querySelector('input[name="retrocession[valid_from]"]').value;
    const validTo = form.querySelector('input[name="retrocession[valid_to]"]').value;
    
    if (validTo && validFrom > validTo) {
        alert('{{ __("validation.date_range_invalid") }}');
        return;
    }
    
    // Validate percentages don't exceed 100%
    let totalPercentage = 0;
    ['cns', 'patient', 'secretary'].forEach(type => {
        if (document.getElementById(type + '_percentage').checked) {
            totalPercentage += parseFloat(document.getElementById(type + '_value').value || 0);
        }
    });
    
    if (totalPercentage > 100) {
        if (!confirm('{{ __("users.total_percentage_exceeds_100") }} (' + totalPercentage.toFixed(2) + '%). {{ __("users.continue_anyway") }}?')) {
            return;
        }
    }
    
    // If all valid, submit the form
    document.querySelector('form').submit();
}

function viewRetrocessionHistory(userId) {
    // Create modal for history
    const modalHtml = `
        <div class="modal fade" id="retrocessionHistoryModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-clock-history me-2"></i>{{ __("users.retrocession_history") }}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="text-center p-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Remove existing modal if any
    const existingModal = document.getElementById('retrocessionHistoryModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    const modal = new bootstrap.Modal(document.getElementById('retrocessionHistoryModal'));
    modal.show();
    
    // Fetch history
    fetch('{{ base_url }}/users/' + userId + '/retrocession-history', {
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        const modalBody = document.querySelector('#retrocessionHistoryModal .modal-body');
        
        if (data.success && data.history.length > 0) {
            let tableHtml = `
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>{{ __("users.valid_from") }}</th>
                                <th>{{ __("users.valid_to") }}</th>
                                <th>{{ __("config.cns") }}</th>
                                <th>{{ __("config.patient") }}</th>
                                <th>{{ __("config.secretary_fees") }}</th>
                                <th>{{ __("config.ceiling") }}</th>
                                <th>{{ __("common.status") }}</th>
                                <th>{{ __("common.created_by") }}</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            data.history.forEach(setting => {
                const cnsValue = setting.cns_type === 'percentage' ? setting.cns_value + '%' : setting.cns_value + ' {{ currency }}';
                const patientValue = setting.patient_type === 'percentage' ? setting.patient_value + '%' : setting.patient_value + ' {{ currency }}';
                const secretaryValue = setting.secretary_type === 'percentage' ? setting.secretary_value + '%' : setting.secretary_value + ' {{ currency }}';
                const ceilingValue = setting.ceiling_enabled ? setting.ceiling_amount + ' {{ currency }}' : '-';
                const status = setting.is_active ? '<span class="badge bg-success">{{ __("common.active") }}</span>' : '<span class="badge bg-secondary">{{ __("common.inactive") }}</span>';
                
                tableHtml += `
                    <tr>
                        <td>${new Date(setting.valid_from).toLocaleDateString()}</td>
                        <td>${setting.valid_to ? new Date(setting.valid_to).toLocaleDateString() : '-'}</td>
                        <td>${cnsValue}</td>
                        <td>${patientValue}</td>
                        <td>${secretaryValue}</td>
                        <td>${ceilingValue}</td>
                        <td>${status}</td>
                        <td>${setting.created_by_username || '-'}</td>
                    </tr>
                `;
            });
            
            tableHtml += '</tbody></table></div>';
            
            if (setting.notes) {
                tableHtml += '<div class="mt-3"><strong>{{ __("common.notes") }}:</strong> ' + setting.notes + '</div>';
            }
            
            modalBody.innerHTML = tableHtml;
        } else {
            modalBody.innerHTML = '<div class="alert alert-info"><i class="bi bi-info-circle me-2"></i>{{ __("users.no_retrocession_history") }}</div>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.querySelector('#retrocessionHistoryModal .modal-body').innerHTML = 
            '<div class="alert alert-danger"><i class="bi bi-exclamation-triangle me-2"></i>{{ __("common.error_loading_data") }}</div>';
    });
}
</script>
{% endblock %}